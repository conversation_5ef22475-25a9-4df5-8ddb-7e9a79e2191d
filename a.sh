#!/bin/bash
# auto_stress.sh
# 动态使用 stress-ng 将 CPU 和内存维持在 40% ~ 60%
# Ubuntu / CentOS 兼容

# ---------------- 配置参数 ----------------
INTERVAL=10
LOW=40
HIGH=60
CPU_WORKERS=0
MEM_GB=0
CPU_PID=0
MEM_PID=0

# ---------------- 检查并安装 stress-ng ----------------
install_stressng() {
    if ! command -v stress-ng &>/dev/null; then
        echo "[INFO] stress-ng 未安装，尝试安装..."
        if [ -f /etc/debian_version ]; then
            sudo apt-get update && sudo apt-get install -y stress-ng
        elif [ -f /etc/redhat-release ]; then
            sudo yum install -y epel-release && sudo yum install -y stress-ng
        else
            echo "[ERROR] 不支持的系统，请手动安装 stress-ng"
            exit 1
        fi
    fi
}

# ---------------- 获取系统信息 ----------------
get_sysinfo() {
    CPU_CORES=$(nproc)
    MEM_TOTAL=$(free -m | awk '/Mem:/ {print $2}')
    echo "[INFO] CPU核心数: $CPU_CORES | 内存总量: ${MEM_TOTAL}MB"
}

# ---------------- 获取实时使用率 ----------------
get_cpu_usage() {
    local idle
    idle=$(top -bn1 | grep "Cpu(s)" | awk '{print $8}' | cut -d. -f1)
    echo $((100 - idle))
}

get_mem_usage() {
    free | awk '/Mem:/ {printf "%.0f", $3/$2 * 100}'
}

# ---------------- 启动/重启 stress-ng ----------------
start_cpu_stress() {
    [ $CPU_PID -ne 0 ] && kill -9 $CPU_PID 2>/dev/null
    if [ $CPU_WORKERS -gt 0 ]; then
        stress-ng --cpu "$CPU_WORKERS" -t 600 >/dev/null 2>&1 &
        CPU_PID=$!
        echo "[CPU] stress-ng --cpu $CPU_WORKERS (PID=$CPU_PID)"
    else
        CPU_PID=0
    fi
}

start_mem_stress() {
    [ $MEM_PID -ne 0 ] && kill -9 $MEM_PID 2>/dev/null
    if [ $MEM_GB -gt 0 ]; then
        local mem_bytes=$((MEM_GB * 1024))M
        stress-ng --vm 10 --vm-bytes "$mem_bytes" --vm-hang 60 -t 600 >/dev/null 2>&1 &
        MEM_PID=$!
        echo "[MEM] stress-ng --vm 10 --vm-bytes $mem_bytes (PID=$MEM_PID)"
    else
        MEM_PID=0
    fi
}

# ---------------- 主逻辑 ----------------
main_loop() {
    while true; do
        cpu=$(get_cpu_usage)
        mem=$(get_mem_usage)
        echo "[INFO] CPU=${cpu}% | MEM=${mem}% | $(date)"

        # --- CPU 调整 ---
        if [ "$cpu" -lt "$LOW" ]; then
            [ $CPU_WORKERS -lt $CPU_CORES ] && CPU_WORKERS=$((CPU_WORKERS + 1))
            start_cpu_stress
        elif [ "$cpu" -gt "$HIGH" ]; then
            [ $CPU_WORKERS -gt 0 ] && CPU_WORKERS=$((CPU_WORKERS - 1))
            start_cpu_stress
        else
            echo "[CPU] CPU 在区间，无需调整"
        fi

        # --- 内存调整 ---
        if [ "$mem" -lt "$LOW" ]; then
            # 每次 +1 GB，最多不超过物理内存 80%
            if [ $((MEM_GB + 1)) -lt $((MEM_TOTAL / 1024 * 80 / 100)) ]; then
                MEM_GB=$((MEM_GB + 1))
                start_mem_stress
            fi
        elif [ "$mem" -gt "$HIGH" ]; then
            [ $MEM_GB -gt 0 ] && MEM_GB=$((MEM_GB - 1))
            start_mem_stress
        else
            echo "[MEM] 内存在区间，无需调整"
        fi

        sleep $INTERVAL
    done
}

# ---------------- 执行 ----------------
install_stressng
get_sysinfo
main_loop
