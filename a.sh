#!/bin/bash
# auto_stress.sh
# 动态使用 stress-ng 将 CPU 和内存维持在目标使用率 (默认50%)
# 使用比例控制算法和动态内存分配
# Ubuntu / CentOS 兼容

# ---------------- 配置参数 ----------------
INTERVAL=10
LOW=40     # 最低使用率阈值
HIGH=60    # 最高使用率阈值
TARGET=50  # 目标使用率 50%
CPU_WORKERS=0
MEM_GB=0
CPU_PID=0
MEM_PID=0

# ---------------- 检查并安装 stress-ng ----------------
install_stressng() {
    if ! command -v stress-ng &>/dev/null; then
        echo "[INFO] stress-ng 未安装，尝试安装..."
        if [ -f /etc/debian_version ]; then
            sudo apt-get update && sudo apt-get install -y stress-ng
        elif [ -f /etc/redhat-release ]; then
            sudo yum install -y epel-release && sudo yum install -y stress-ng
        else
            echo "[ERROR] 不支持的系统，请手动安装 stress-ng"
            exit 1
        fi
    fi
}

# ---------------- 获取系统信息 ----------------
get_sysinfo() {
    CPU_CORES=$(nproc)
    MEM_TOTAL=$(free -m | awk '/Mem:/ {print $2}')
    echo "[INFO] CPU核心数: $CPU_CORES | 内存总量: ${MEM_TOTAL}MB"
}

# ---------------- 获取实时使用率 ----------------
get_cpu_usage() {
    local idle
    idle=$(top -bn1 | grep "Cpu(s)" | awk '{print $8}' | cut -d. -f1)
    echo $((100 - idle))
}

get_mem_usage() {
    free | awk '/Mem:/ {printf "%.0f", $3/$2 * 100}'
}

# ---------------- 启动/重启 stress-ng ----------------
start_cpu_stress() {
    if [ $CPU_PID -ne 0 ]; then
        kill -9 $CPU_PID >/dev/null 2>&1
        wait $CPU_PID 2>/dev/null  # 等待进程完全结束，避免显示 Killed 消息
    fi
    if [ $CPU_WORKERS -gt 0 ]; then
        stress-ng --cpu "$CPU_WORKERS" -t 600 >/dev/null 2>&1 &
        CPU_PID=$!
        echo "[CPU] stress-ng --cpu $CPU_WORKERS (PID=$CPU_PID)"
    else
        CPU_PID=0
    fi
}

start_mem_stress() {
    if [ $MEM_PID -ne 0 ]; then
        kill -9 $MEM_PID >/dev/null 2>&1
        wait $MEM_PID 2>/dev/null  # 等待进程完全结束，避免显示 Killed 消息
    fi
    if [ $MEM_GB -gt 0 ]; then
        local mem_bytes=$((MEM_GB * 1024))M
        stress-ng --vm 10 --vm-bytes "$mem_bytes" --vm-hang 60 -t 600 >/dev/null 2>&1 &
        MEM_PID=$!
        echo "[MEM] stress-ng --vm 10 --vm-bytes $mem_bytes (PID=$MEM_PID)"
    else
        MEM_PID=0
    fi
}

# ---------------- 主逻辑 ----------------
main_loop() {
    while true; do
        cpu=$(get_cpu_usage)
        mem=$(get_mem_usage)
        echo "[INFO] CPU=${cpu}% | MEM=${mem}% | $(date)"

        # --- CPU 调整 (比例控制) ---
        if [ "$cpu" -ge "$LOW" ] && [ "$cpu" -le "$HIGH" ]; then
            echo "[CPU] CPU 在区间 (${cpu}%)，无需调整"
        else
            # 超出区间，需要调整到目标值
            diff=$((TARGET - cpu))
            # 按比例调节：差值/5 作为增加量
            adjustment=$((diff / 5))
            # 确保至少调整1个核心（如果差值不为0）
            if [ $adjustment -eq 0 ] && [ $diff -gt 0 ]; then
                adjustment=1
            elif [ $adjustment -eq 0 ] && [ $diff -lt 0 ]; then
                adjustment=-1
            fi

            new_workers=$((CPU_WORKERS + adjustment))
            # 限制在合理范围内
            if [ $new_workers -lt 0 ]; then
                new_workers=0
            elif [ $new_workers -gt $CPU_CORES ]; then
                new_workers=$CPU_CORES
            fi

            if [ $new_workers -ne $CPU_WORKERS ]; then
                CPU_WORKERS=$new_workers
                start_cpu_stress
                echo "[CPU] 超出区间 (${cpu}%), 差值=${diff}%, 调整=${adjustment}, 新核心数=${CPU_WORKERS}"
            else
                echo "[CPU] CPU 已达到边界，无需调整"
            fi
        fi

        # --- 内存调整 (动态分配) ---
        if [ "$mem" -ge "$LOW" ] && [ "$mem" -le "$HIGH" ]; then
            echo "[MEM] 内存在区间 (${mem}%)，无需调整"
        else
            # 超出区间，需要调整到目标值
            mem_diff=$((TARGET - mem))
            # 计算目标内存使用量 (MB)
            target_mem_mb=$((MEM_TOTAL * TARGET / 100))
            current_used_mb=$((MEM_TOTAL * mem / 100))
            need_mb=$((target_mem_mb - current_used_mb))

            # 转换为 GB (向上取整)
            if [ $need_mb -gt 0 ]; then
                need_gb=$(((need_mb + 1023) / 1024))
            else
                need_gb=$((need_mb / 1024))
            fi

            new_mem_gb=$((MEM_GB + need_gb))

            # 限制在合理范围内 (最多使用80%物理内存)
            max_mem_gb=$((MEM_TOTAL * 80 / 100 / 1024))
            if [ $new_mem_gb -lt 0 ]; then
                new_mem_gb=0
            elif [ $new_mem_gb -gt $max_mem_gb ]; then
                new_mem_gb=$max_mem_gb
            fi

            if [ $new_mem_gb -ne $MEM_GB ]; then
                MEM_GB=$new_mem_gb
                start_mem_stress
                echo "[MEM] 超出区间 (${mem}%), 差值=${mem_diff}%, 需要=${need_mb}MB, 新分配=${MEM_GB}GB"
            else
                echo "[MEM] 内存已达到边界，无需调整"
            fi
        fi

        sleep $INTERVAL
    done
}

# ---------------- 执行 ----------------
install_stressng
get_sysinfo
main_loop
